// 内容脚本 - 在购物网站上注入浮动按钮
;(function () {
  'use strict'

  let floatingBtn = null
  let modal = null
  let isProcessing = false

  // 添加调试日志
  console.log('🔧 Content script 开始加载...')

  // 确认添加商品 - 全局函数
  window.confirmAdd = async function () {
    console.log('🚀 confirmAdd 函数被调用!')

    if (isProcessing) {
      console.log('⚠️ 正在处理中，忽略重复点击')
      return
    }

    console.log('✅ 开始确认添加商品')
    isProcessing = true
    const confirmBtn = document.getElementById('confirm-btn')
    if (!confirmBtn) {
      console.error('❌ 找不到确认按钮')
      return
    }

    console.log('✅ 找到确认按钮:', confirmBtn)
    const originalText = confirmBtn.textContent

    confirmBtn.textContent = '添加中...'
    confirmBtn.disabled = true

    const modalContent = modal?.querySelector('.modal-content')
    if (modalContent) {
      modalContent.classList.add('loading')
      console.log('✅ 添加loading样式')
    }

    try {
      console.log('📊 开始获取商品信息...')
      const productInfo = getProductInfo()
      const languageSelect = document.getElementById('language-select')
      const language = languageSelect ? languageSelect.value : 'zh'

      console.log('📦 商品信息:', productInfo)
      console.log('🌐 选择的语言:', language)

      // 发送消息给background script
      const messageData = {
        action: 'addProduct',
        data: {
          url: productInfo.url,
          language: language,
          title: productInfo.title,
          site: productInfo.site
        }
      }

      console.log('📤 发送消息到background script:', messageData)

      const response = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(messageData, (response) => {
          console.log('📥 收到background script响应:', response)
          if (chrome.runtime.lastError) {
            console.error('❌ Chrome runtime错误:', chrome.runtime.lastError)
            reject(new Error(chrome.runtime.lastError.message))
          } else {
            resolve(response)
          }
        })
      })

      if (response && response.success) {
        console.log('✅ 商品添加成功!')
        window.showStatus('商品添加成功！', 'success')
        if (window.hideModal) {
          window.hideModal()
        }
      } else {
        console.log('❌ 商品添加失败:', response)
        window.showStatus('添加失败: ' + (response?.error || '未知错误'), 'error')
      }
    } catch (error) {
      console.error('💥 添加商品失败:', error)
      window.showStatus('添加失败: ' + error.message, 'error')
    } finally {
      isProcessing = false
      confirmBtn.textContent = originalText
      confirmBtn.disabled = false
      if (modalContent) {
        modalContent.classList.remove('loading')
      }
      console.log('🏁 confirmAdd 函数执行完成')
    }
  }

  // 立即检查函数是否正确定义
  console.log('🔍 检查 window.confirmAdd:', typeof window.confirmAdd)
  console.log('🔍 window.confirmAdd 函数:', window.confirmAdd)

  // 网站配置
  const siteConfigs = {
    'taobao.com': {
      name: '淘宝',
      titleSelector: [
        'h1[data-spm="1000983"]',
        '.tb-main-title',
        'h1.tb-item-title',
        '.tb-detail-hd h1'
      ],
      priceSelector: ['.tb-rmb-num', '.price-current', '.tm-price-current']
    },
    'tmall.com': {
      name: '天猫',
      titleSelector: [
        'h1[data-spm="1000983"]',
        '.tb-main-title',
        'h1.tb-item-title',
        '.tb-detail-hd h1'
      ],
      priceSelector: ['.tb-rmb-num', '.price-current', '.tm-price-current']
    },
    'jd.com': {
      name: '京东',
      titleSelector: [
        '.sku-name',
        'h1.product-intro-name',
        '.p-name a',
        '.itemInfo-wrap .sku-name'
      ],
      priceSelector: ['.price', '.p-price .price', '.summary-price .price']
    },
    '1688.com': {
      name: '1688',
      titleSelector: ['.d-title', '.subject', '.detail-title', '.od-main .subject'],
      priceSelector: ['.price', '.price-range', '.offer-price']
    },
    'vip.com': {
      name: '唯品会',
      titleSelector: ['.pib-title-detail', '.goods-title', '.product-title'],
      priceSelector: ['.price-current', '.goods-price']
    },
    'suning.com': {
      name: '苏宁',
      titleSelector: ['.proinfo-title', '.product-title h1'],
      priceSelector: ['.mainprice', '.price-current']
    },
    'dangdang.com': {
      name: '当当',
      titleSelector: ['.name_info h1', '.product_main .name'],
      priceSelector: ['.price_n', '.sale_price']
    }
  }

  // 初始化
  function init() {
    if (shouldShowButton()) {
      createFloatingButton()
      createModal()
    }
  }

  // 判断是否应该显示按钮
  function shouldShowButton() {
    const hostname = window.location.hostname
    return Object.keys(siteConfigs).some((domain) => hostname.includes(domain))
  }

  // 创建浮动按钮
  function createFloatingButton() {
    if (floatingBtn) return

    floatingBtn = document.createElement('button')
    floatingBtn.id = 'quick-add-btn'
    floatingBtn.innerHTML = '🛒'
    floatingBtn.title = '快速添加商品到商店'

    floatingBtn.addEventListener('click', showModal)

    document.body.appendChild(floatingBtn)
  }

  // 创建确认弹窗
  function createModal() {
    if (modal) return

    modal = document.createElement('div')
    modal.id = 'quick-add-modal'
    modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🛒 添加商品到商店</h3>
                    <button class="close-btn" onclick="hideModal()">&times;</button>
                </div>
                <div class="product-info">
                    <div class="product-title" id="product-title">正在获取商品信息...</div>
                    <div class="product-url" id="product-url"></div>
                </div>
                <div class="language-select">
                    <label for="language-select">选择语言:</label>
                    <select id="language-select">
                        <option value="zh">中文</option>
                        <option value="en">英文</option>
                    </select>
                </div>
                <div class="modal-actions">
                    <button class="btn btn-cancel" onclick="hideModal()">取消</button>
                    <button class="btn btn-confirm" id="confirm-btn">确认添加</button>
                </div>
            </div>
        `

    // 点击背景关闭弹窗
    modal.addEventListener('click', function (e) {
      if (e.target === modal) {
        hideModal()
      }
    })

    document.body.appendChild(modal)

    // 添加确认按钮事件监听器
    const confirmBtn = modal.querySelector('#confirm-btn')
    if (confirmBtn) {
      console.log('✅ 找到确认按钮，添加事件监听器')
      confirmBtn.addEventListener('click', function () {
        console.log('🖱️ 确认按钮被点击，调用 window.confirmAdd')
        if (window.confirmAdd) {
          window.confirmAdd()
        } else {
          console.error('❌ window.confirmAdd 函数不存在!')
        }
      })
    } else {
      console.error('❌ 找不到确认按钮!')
    }

    // 将函数添加到全局作用域
    window.hideModal = hideModal
  }

  // 显示弹窗
  function showModal() {
    if (!modal || isProcessing) return

    // 获取商品信息
    const productInfo = getProductInfo()

    document.getElementById('product-title').textContent = productInfo.title
    document.getElementById('product-url').textContent = productInfo.url

    // 加载默认语言设置
    chrome.storage.sync.get(['language'], function (result) {
      const languageSelect = document.getElementById('language-select')
      if (languageSelect && result.language) {
        languageSelect.value = result.language
      }
    })

    modal.classList.add('show')
  }

  // 隐藏弹窗
  function hideModal() {
    if (modal) {
      modal.classList.remove('show')
    }
  }

  // 获取商品信息
  function getProductInfo() {
    const hostname = window.location.hostname
    const config = Object.entries(siteConfigs).find(([domain]) => hostname.includes(domain))?.[1]

    let title = '未知商品'

    if (config && config.titleSelector) {
      // 支持数组格式的选择器
      const selectors = Array.isArray(config.titleSelector)
        ? config.titleSelector
        : [config.titleSelector]

      for (const selector of selectors) {
        const titleElement = document.querySelector(selector)
        if (titleElement && titleElement.textContent.trim()) {
          title = titleElement.textContent.trim()
          break
        }
      }
    }

    // 如果没有找到标题，尝试从页面标题获取
    if (title === '未知商品') {
      title = document.title.split('-')[0].split('_')[0].trim()
    }

    // 清理标题，移除多余的空白字符
    title = title.replace(/\s+/g, ' ').trim()

    return {
      title: title,
      url: window.location.href,
      site: config ? config.name : '未知网站'
    }
  }

  // 显示状态消息
  window.showStatus = function (message, type) {
    const statusDiv = document.createElement('div')
    statusDiv.className = `status-message status-${type}`
    statusDiv.textContent = message

    document.body.appendChild(statusDiv)

    setTimeout(() => {
      if (statusDiv.parentNode) {
        statusDiv.parentNode.removeChild(statusDiv)
      }
    }, 3000)
  }

  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init)
  } else {
    init()
  }

  // 监听来自popup的消息
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'showModal') {
      showModal()
      sendResponse({ success: true })
    }
  })

  // 监听页面变化（SPA应用）
  let lastUrl = location.href
  new MutationObserver(() => {
    const url = location.href
    if (url !== lastUrl) {
      lastUrl = url
      setTimeout(init, 1000) // 延迟初始化，等待页面内容加载
    }
  }).observe(document, { subtree: true, childList: true })
})()
