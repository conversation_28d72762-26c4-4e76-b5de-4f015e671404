# 物流国家分区多选产品功能修改说明

## 修改概述

根据后端接口调整，将物流国家分区的产品选择从单选改为多选，字段从 `productId` 改为 `productIds`（JSON格式）。

## 主要变更

### 1. API接口类型定义 (`src/api/mall/agent/logisticsZone/index.ts`)

```typescript
// 修改前
export interface LogisticsZoneVO {
  productId: number // 产品编号
}

// 修改后
export interface LogisticsZoneVO {
  productIds: string // 产品编号列表(JSON格式)
}
```

### 2. 表单组件 (`src/views/mall/agent/logisticsZone/LogisticsZoneForm.vue`)

#### UI界面变更
- 将产品选择器改为多选模式
- 添加折叠标签显示，最多显示3个标签
- 显示已选择产品数量提示

```vue
<el-select 
  v-model="selectedProductIds" 
  placeholder="请选择产品编号（可多选）"
  multiple
  collapse-tags
  collapse-tags-tooltip
  :max-collapse-tags="3"
>
  <el-option
    v-for="item in logisticsProductList"
    :key="item.id"
    :label="`${item.nameZh || item.name} (${item.productCode})`"
    :value="item.id"
  />
</el-select>
```

#### 数据结构变更
```typescript
// 表单数据
const formData = ref({
  productIds: undefined as string | undefined, // JSON格式存储
})

// 选中的产品ID数组
const selectedProductIds = ref<number[]>([])
```

#### 表单验证
```typescript
productIds: [
  { 
    validator: (_rule: any, value: string, callback: Function) => {
      if (!selectedProductIds.value || selectedProductIds.value.length === 0) {
        callback(new Error('请至少选择一个产品'))
      } else {
        callback()
      }
    }, 
    trigger: 'change' 
  }
]
```

#### 数据处理逻辑
- **提交时**: 将 `selectedProductIds` 数组转换为JSON字符串保存到 `productIds`
- **编辑时**: 解析 `productIds` JSON字符串到 `selectedProductIds` 数组
- **监听器**: 自动同步 `selectedProductIds` 变化到 `formData.productIds`

### 3. 列表页面 (`src/views/mall/agent/logisticsZone/index.vue`)

#### 表格显示变更
```vue
<el-table-column label="产品编号" align="center" prop="productIds" min-width="120px">
  <template #default="scope">
    <div v-if="scope.row.productIds">
      <el-tag 
        v-for="productId in parseProductIds(scope.row.productIds)" 
        :key="productId"
        size="small"
        class="mr-1 mb-1"
      >
        {{ getProductName(productId) }}
      </el-tag>
    </div>
    <span v-else>-</span>
  </template>
</el-table-column>
```

#### 辅助方法
```typescript
/** 解析产品ID字符串 */
const parseProductIds = (productIdsStr: string): number[] => {
  try {
    return JSON.parse(productIdsStr)
  } catch (e) {
    console.warn('解析产品ID失败:', e)
    return []
  }
}

/** 获取产品名称 */
const getProductName = (productId: number): string => {
  const product = productList.value.find(p => p.id === productId)
  return product ? `${product.nameZh || product.name} (${product.productCode})` : `ID:${productId}`
}
```

## 后端接口字段说明

```java
@Schema(description = "产品编号列表(JSON格式)", example = "[1601,1602,1603]")
private String productIds;
```

## 数据示例

### 前端发送数据
```json
{
  "countryCode": "US",
  "productIds": "[1601,1602,1603]",
  "zoneCode": "NORMAL",
  "zoneName": "正常区域"
}
```

### 后端返回数据
```json
{
  "id": 1,
  "countryCode": "US", 
  "productIds": "[1601,1602,1603]",
  "zoneCode": "NORMAL",
  "zoneName": "正常区域"
}
```

## 用户体验改进

1. **多选支持**: 用户可以一次性为多个产品配置相同的分区规则
2. **标签折叠**: 当选择产品较多时，自动折叠显示，避免界面混乱
3. **产品信息**: 显示产品名称和编码，便于用户识别
4. **数量提示**: 实时显示已选择的产品数量
5. **验证提示**: 必须至少选择一个产品才能提交

## 技术特点

1. **数据一致性**: 通过监听器确保UI状态与表单数据同步
2. **错误处理**: 对JSON解析失败进行容错处理
3. **类型安全**: 使用TypeScript确保类型安全
4. **性能优化**: 使用折叠标签避免大量DOM渲染
