<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1600px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <!-- 第一行：基础信息 -->
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="适用国家" prop="countryCodes">
            <div class="w-full">
              <!-- 已选择的国家显示 -->
              <div v-if="selectedCountries.length > 0" class="mb-2">
                <el-tag
                  v-for="country in selectedCountries.slice(0, 3)"
                  :key="country.code"
                  closable
                  @close="removeCountry(country.code)"
                  class="mr-1 mb-1"
                  size="small"
                >
                  {{ country.name }}
                </el-tag>
                <el-tag v-if="selectedCountries.length > 3" class="mr-1 mb-1" size="small">
                  +{{ selectedCountries.length - 3 }}个
                </el-tag>
              </div>

              <!-- 选择按钮 -->
              <el-button
                @click="openCountryDialog"
                class="w-full"
                :type="selectedCountries.length > 0 ? 'primary' : 'default'"
              >
                <Icon icon="ep:plus" class="mr-1" />
                {{
                  selectedCountries.length > 0
                    ? `已选择 ${selectedCountries.length} 个国家`
                    : '选择国家'
                }}
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分区编码" prop="zoneCode">
            <el-input v-model="formData.zoneCode" placeholder="如：Zone1（可选）" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="时效" prop="transitTime">
            <el-input v-model="formData.transitTime" placeholder="如：12-20天" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行：计费配置 -->
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="计费方式" prop="chargeType">
            <el-select v-model="formData.chargeType" placeholder="请选择计费方式" class="w-full">
              <el-option label="重量计费" value="WEIGHT" />
              <el-option label="体积计费" value="VOLUME" />
              <el-option label="件数计费" value="PIECE" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="价格类型" prop="priceType">
            <el-select v-model="formData.priceType" placeholder="请选择价格类型" class="w-full">
              <el-option label="阶梯计费" value="TIERED" />
              <el-option label="递增计费" value="INCREMENTAL" />
              <el-option label="混合计费" value="TIERED_INCREMENTAL" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="体积基数" prop="volumeBase">
            <el-input v-model="formData.volumeBase" placeholder="如：8000" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第三行：首重配置 -->
      <el-row :gutter="16">
        <el-col :span="6">
          <el-form-item label="首重/件数量" prop="firstUnit">
            <el-input-number
              v-model="formData.firstUnit"
              placeholder="单位G"
              :min="0"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="首重/件价格" prop="firstPrice">
            <el-input-number
              v-model="formData.firstPrice"
              placeholder="单位分"
              :min="0"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="燃油费率" prop="fuelFeeRate">
            <el-input-number
              v-model="formData.fuelFeeRate"
              placeholder="如：0.15"
              :min="0"
              :max="1"
              :step="0.01"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第四行：续重配置（仅递增模式显示） -->
      <el-row :gutter="16" v-if="formData.priceType === 'INCREMENTAL'">
        <el-col :span="6">
          <el-form-item label="续重/件单位" prop="additionalUnit">
            <el-input-number
              v-model="formData.additionalUnit"
              placeholder="单位G"
              :min="0"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="续重/件价格" prop="additionalPrice">
            <el-input-number
              v-model="formData.additionalPrice"
              placeholder="单位分"
              :min="0"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <!-- 占位，保持布局对齐 -->
        </el-col>
      </el-row>

      <!-- 第五行：重量限制 -->
      <el-row :gutter="16">
        <el-col :span="6">
          <el-form-item label="最小重量(g)" prop="minWeight">
            <el-input-number
              v-model="formData.minWeight"
              placeholder="如：100"
              :min="0"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="最大重量(g)" prop="maxWeight">
            <el-input-number
              v-model="formData.maxWeight"
              placeholder="如：30000"
              :min="0"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="formData.sort" placeholder="如：1" :min="0" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第六行：各种费用 -->
      <el-row :gutter="16">
        <el-col :span="6">
          <el-form-item label="挂号费(分)" prop="registrationFee">
            <el-input-number
              v-model="formData.registrationFee"
              placeholder="如：0"
              :min="0"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="操作费(分)" prop="operationFee">
            <el-input-number
              v-model="formData.operationFee"
              placeholder="如：0"
              :min="0"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="服务费(分)" prop="serviceFee">
            <el-input-number
              v-model="formData.serviceFee"
              placeholder="如：0"
              :min="0"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="清关费(分)" prop="customsFee">
            <el-input-number
              v-model="formData.customsFee"
              placeholder="如：0"
              :min="0"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第七行：清关费和关税 -->
      <el-row :gutter="16">
        <el-col :span="6">
          <el-form-item label="是否预收关税" prop="prepayTariff">
            <el-radio-group v-model="formData.prepayTariff">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="关税税率" prop="tariffRate">
            <el-input-number
              v-model="formData.tariffRate"
              placeholder="如：0.10"
              :min="0"
              :max="1"
              :step="0.01"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="折扣" prop="discountRate">
            <el-input-number
              v-model="formData.discountRate"
              placeholder="如：0.10"
              :step="0.01"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 第八行 申报价值范围 -->
      <el-row :gutter="16">
        <el-col :span="6">
          <el-form-item label="最低申报价值" prop="minDeclareValue">
            <el-input-number
              v-model="formData.minDeclareValue"
              placeholder="单位分"
              :min="0"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="最高申报价值" prop="maxDeclareValue">
            <el-input-number
              v-model="formData.maxDeclareValue"
              placeholder="单位分"
              :min="0"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="妥投率" prop="deliveryRate">
            <el-input-number
              v-model="formData.deliveryRate"
              placeholder="如：0.98"
              :min="0"
              :max="100"
              :step="0.1"
              :precision="2"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 阶梯价格配置（仅阶梯模式显示） -->
      <el-row v-if="formData.priceType === 'TIERED'">
        <el-col :span="24">
          <el-form-item label="阶梯价格配置" prop="priceConfig">
            <div class="border border-gray-200 rounded p-4 bg-gray-50 tiered-config">
              <!-- 添加阶梯按钮 -->
              <div class="mb-3">
                <el-button type="primary" size="small" @click="addTieredItem">
                  <Icon icon="ep:plus" class="mr-1" /> 添加
                </el-button>
                <span class="text-sm text-gray-500 ml-2">配置不同重量区间的价格</span>
              </div>

              <!-- 阶梯价格列表 -->
              <div v-if="tieredConfig.length === 0" class="text-center py-4 text-gray-500">
                <div class="text-xs mt-1">点击"添加"按钮开始配置</div>
              </div>

              <div v-for="(item, index) in tieredConfig" :key="index" class="mb-4">
                <!-- 第一行：基础配置 -->
                <el-row :gutter="16" class="tiered-row mb-2">
                  <el-col :span="4" class="tiered-col">
                    <el-form-item
                      :label="`阶梯${index + 1}起始`"
                      class="tiered-form-item"
                      :label-width="'70px'"
                    >
                      <el-input-number
                        v-model="item.tierStart"
                        placeholder="如：0"
                        :min="0"
                        class="tiered-input"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4" class="tiered-col">
                    <el-form-item label="结束重量" class="tiered-form-item" :label-width="'85px'">
                      <el-input-number
                        v-model="item.tierEnd"
                        placeholder="如：500"
                        :min="0"
                        class="tiered-input"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="3" class="tiered-col">
                    <el-form-item label="单价" class="tiered-form-item" :label-width="'38px'">
                      <el-input-number
                        v-model="item.unitPrice"
                        placeholder="如：12000"
                        :min="0"
                        class="tiered-input"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="3" class="tiered-col">
                    <el-form-item label="挂号费" class="tiered-form-item" :label-width="'65px'">
                      <el-input-number
                        v-model="item.registrationFee"
                        placeholder="可选"
                        :min="0"
                        class="tiered-input"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4" class="tiered-col">
                    <el-form-item label="进位制" class="tiered-form-item" :label-width="'70px'">
                      <el-input-number
                        v-model="item.roundingUnit"
                        placeholder="如：100"
                        :min="1"
                        class="tiered-input"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4" class="tiered-col">
                    <el-form-item label="最低计费重" class="tiered-form-item" :label-width="'90px'">
                      <el-input-number
                        v-model="item.minChargeWeight"
                        placeholder="如：100"
                        :min="0"
                        class="tiered-input"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="1" class="tiered-col tiered-button-col">
                    <el-button
                      link
                      type="danger"
                      size="small"
                      @click="removeTieredItem(index)"
                      class="tiered-delete-btn"
                    >
                      <Icon icon="ep:delete" class="mr-1" />
                    </el-button>
                  </el-col>
                  <!-- <el-col :span="14">
                    <div class="text-xs text-gray-500 pt-2">
                      进位制和最低计费重为可选配置，未填写时使用主配置中的值
                    </div>
                  </el-col> -->
                </el-row>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 混合计费配置（仅混合计费模式显示） -->
      <el-row v-if="formData.priceType === 'TIERED_INCREMENTAL'">
        <el-col :span="24">
          <el-form-item label="混合计费配置" prop="priceConfig">
            <div class="border border-gray-200 rounded p-4 bg-gray-50 tiered-config">
              <!-- 添加阶梯按钮 -->
              <div class="mb-3">
                <el-button type="primary" size="small" @click="addTieredIncrementalItem">
                  <Icon icon="ep:plus" class="mr-1" /> 添加重量阶梯
                </el-button>
                <span class="text-sm text-gray-500 ml-2">配置不同重量区间的首重续重价格</span>
              </div>

              <!-- 混合计费列表 -->
              <div
                v-if="tieredIncrementalConfig.length === 0"
                class="text-center py-4 text-gray-500"
              >
                <div class="text-sm">暂无混合计费配置</div>
                <div class="text-xs mt-1">点击上方"添加重量阶梯"按钮开始配置</div>
              </div>

              <div v-for="(item, index) in tieredIncrementalConfig" :key="index" class="mb-4">
                <!-- 阶梯标题和删除按钮 -->
                <div class="flex items-center justify-between mb-3">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-700">阶梯 {{ index + 1 }}</span>
                    <span class="text-xs text-gray-500 ml-2">配置该重量区间的首重续重价格</span>
                  </div>
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeTieredIncrementalItem(index)"
                    class="tiered-delete-btn"
                  >
                    <Icon icon="ep:delete" class="mr-1" /> 删除
                  </el-button>
                </div>

                <!-- 第一行：重量区间和首重配置 -->
                <el-row :gutter="16" class="mb-3">
                  <el-col :span="4" class="tiered-col">
                    <el-form-item label="起始重量" class="tiered-form-item" :label-width="'90px'">
                      <el-input-number
                        v-model="item.tierStart"
                        placeholder="如：1"
                        :min="0"
                        class="tiered-input"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4" class="tiered-col">
                    <el-form-item label="结束重量" class="tiered-form-item" :label-width="'90px'">
                      <el-input-number
                        v-model="item.tierEnd"
                        placeholder="如：15000"
                        :min="0"
                        class="tiered-input"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4" class="tiered-col">
                    <el-form-item label="首重" class="tiered-form-item" :label-width="'90px'">
                      <el-input-number
                        v-model="item.firstWeight"
                        placeholder="如：100"
                        :min="0"
                        class="tiered-input"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4" class="tiered-col">
                    <el-form-item label="首重价格" class="tiered-form-item" :label-width="'100px'">
                      <el-input-number
                        v-model="item.firstPrice"
                        placeholder="如：14210"
                        :min="0"
                        class="tiered-input"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4" class="tiered-col">
                    <el-form-item label="续重" class="tiered-form-item" :label-width="'90px'">
                      <el-input-number
                        v-model="item.additionalWeight"
                        placeholder="如：100"
                        :min="0"
                        class="tiered-input"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4" class="tiered-col">
                    <el-form-item label="续重价格" class="tiered-form-item" :label-width="'100px'">
                      <el-input-number
                        v-model="item.additionalPrice"
                        placeholder="如：2610"
                        :min="0"
                        class="tiered-input"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 第二行：新增字段 -->
                <el-row :gutter="16" class="mb-3">
                  <el-col :span="4" class="tiered-col">
                    <el-form-item label="进位制" class="tiered-form-item" :label-width="'90px'">
                      <el-input-number
                        v-model="item.roundingUnit"
                        placeholder="如：100"
                        :min="1"
                        class="tiered-input"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4" class="tiered-col">
                    <el-form-item label="最低计费重" class="tiered-form-item" :label-width="'90px'">
                      <el-input-number
                        v-model="item.minChargeWeight"
                        placeholder="如：100"
                        :min="0"
                        class="tiered-input"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4" class="tiered-col">
                    <el-form-item label="挂号费" class="tiered-form-item" :label-width="'90px'">
                      <el-input-number
                        v-model="item.registrationFee"
                        placeholder="可选"
                        :min="0"
                        class="tiered-input"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 分隔线（除了最后一个阶梯） -->
                <div
                  v-if="index < tieredIncrementalConfig.length - 1"
                  class="border-b border-gray-200 my-4"
                ></div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 尺寸限制配置 -->
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="尺寸限制配置" prop="sizeRestrictions">
            <div class="border border-gray-200 rounded p-4 bg-gray-50">
              <div class="text-sm text-gray-500 mb-3">
                配置该国家/分区的特殊尺寸限制，单位：cm（可选）
              </div>
              <el-row :gutter="16" class="mb-3">
                <el-col :span="4">
                  <el-form-item label="最小长度" class="mb-2">
                    <el-input-number
                      v-model="sizeRestrictionsConfig.minLength"
                      placeholder="cm"
                      :min="0"
                      :max="999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="最小宽度" class="mb-2">
                    <el-input-number
                      v-model="sizeRestrictionsConfig.minWidth"
                      placeholder="cm"
                      :min="0"
                      :max="999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="最小高度" class="mb-2">
                    <el-input-number
                      v-model="sizeRestrictionsConfig.minHeight"
                      placeholder="cm"
                      :min="0"
                      :max="999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="最大长度" class="mb-2">
                    <el-input-number
                      v-model="sizeRestrictionsConfig.maxLength"
                      placeholder="cm"
                      :min="0"
                      :max="999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="最大宽度" class="mb-2">
                    <el-input-number
                      v-model="sizeRestrictionsConfig.maxWidth"
                      placeholder="cm"
                      :min="0"
                      :max="999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="最大高度" class="mb-2">
                    <el-input-number
                      v-model="sizeRestrictionsConfig.maxHeight"
                      placeholder="cm"
                      :min="0"
                      :max="999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="16">
                <el-col :span="4">
                  <el-form-item label="最大单边" class="mb-2">
                    <el-input-number
                      v-model="sizeRestrictionsConfig.maxSingleSide"
                      placeholder="cm"
                      :min="0"
                      :max="999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="第二长边限制" class="mb-2">
                    <el-input-number
                      v-model="sizeRestrictionsConfig.maxSecondLongestSide"
                      placeholder="cm"
                      :min="0"
                      :max="9999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="最大三边和" class="mb-2">
                    <el-input-number
                      v-model="sizeRestrictionsConfig.maxTotalDimension"
                      placeholder="cm"
                      :min="0"
                      :max="9999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="长 + 2*(宽+高)" class="mb-2" :label-width="'130px'">
                    <el-input-number
                      v-model="sizeRestrictionsConfig.maxLengthPlusDoubleGirth"
                      placeholder="cm"
                      :min="0"
                      :max="9999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>

                <el-col :span="5">
                  <el-form-item label="超尺寸附加费" class="mb-2">
                    <el-input-number
                      v-model="sizeRestrictionsConfig.oversizeFee"
                      placeholder="cny"
                      :min="0"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-col :span="24">
        <el-form-item label="时效分布配置" prop="timelinessInfo">
          <div class="border border-gray-200 rounded p-4 bg-gray-50">
            <!-- 总体妥投率 -->
            <el-row :gutter="16" class="mb-4">
              <el-col :span="12">
                <el-form-item label="总妥投率(%)" class="mb-0">
                  <el-input-number
                    v-model="timelinessConfig.deliveryRate"
                    placeholder="如：98.1"
                    :min="0"
                    :max="100"
                    :step="0.1"
                    :precision="2"
                    style="width: 200px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <div class="text-sm text-gray-500 pt-2">
                  配置各时间段的妥投率分布，总和应为100%
                </div>
              </el-col>
            </el-row>

            <!-- 时效分布列表 -->
            <div class="mb-3">
              <el-button type="primary" size="small" @click="addTimelinessItem">
                <Icon icon="ep:plus" class="mr-1" /> 添加时间段
              </el-button>
            </div>

            <div
              v-for="(item, index) in timelinessConfig.timelinessInfos"
              :key="index"
              class="mb-2"
            >
              <el-row :gutter="16" class="items-center">
                <el-col :span="6">
                  <el-input
                    v-model="item.timeInterval"
                    placeholder="如：8-12"
                    style="width: 120px"
                  />
                </el-col>
                <el-col :span="6">
                  <el-input-number
                    v-model="item.rate"
                    placeholder="如：84.6"
                    :min="0"
                    :max="100"
                    :step="0.1"
                    :precision="2"
                    style="width: 120px"
                  />
                </el-col>
                <el-col :span="12">
                  <el-button type="danger" size="small" @click="removeTimelinessItem(index)">
                    <Icon icon="ep:delete" class="mr-1" /> 删除
                  </el-button>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form-item>
      </el-col>

      <!-- 第八行：时间和状态 -->
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="生效时间" prop="effectiveTime">
            <el-date-picker
              v-model="formData.effectiveTime"
              type="date"
              value-format="x"
              placeholder="选择生效时间"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="失效时间" prop="expireTime">
            <el-date-picker
              v-model="formData.expireTime"
              type="date"
              value-format="x"
              placeholder="选择失效时间"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="开启状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <!-- 国家选择弹窗 -->
  <CountrySelectDialog ref="countrySelectRef" @confirm="handleCountrySelect" />
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { LogisticsProductApi } from '@/api/mall/agent/logisticsProduct'
import { CountryApi } from '@/api/mall/agent/country'
import CountrySelectDialog from './CountrySelectDialog.vue'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改

// 国家选择相关
const countrySelectRef = ref()
const selectedCountries = ref<any[]>([])

interface CountryItem {
  code: string
  name: string
}

// 时效分布配置
const timelinessConfig = ref({
  deliveryRate: 98.0 as number | undefined,
  timelinessInfos: [] as Array<{ timeInterval: string; rate: number }>
})

// 阶梯价格配置
const tieredConfig = ref(
  [] as Array<{
    tierStart: number
    tierEnd: number
    unitPrice: number
    registrationFee?: number // 挂号费(分)，可选字段
    roundingUnit?: number // 进位制(g)，可选字段
    minChargeWeight?: number // 最低计费重(g)，可选字段
  }>
)

// 混合计费配置
const tieredIncrementalConfig = ref(
  [] as Array<{
    tierStart: number // 起始重量(g)
    tierEnd: number // 结束重量(g)
    firstWeight: number // 首重(g)
    firstPrice: number // 首重价格(分)
    additionalWeight: number // 续重(g)
    additionalPrice: number // 续重价格(分)
    registrationFee?: number // 挂号费(分)，可选字段
    roundingUnit?: number // 进位制(g)，可选字段
    minChargeWeight?: number // 最低计费重(g)，可选字段
  }>
)

// 尺寸限制配置
const sizeRestrictionsConfig = ref({
  minLength: undefined as number | undefined,
  minWidth: undefined as number | undefined,
  minHeight: undefined as number | undefined,
  maxLength: undefined as number | undefined,
  maxWidth: undefined as number | undefined,
  maxHeight: undefined as number | undefined,

  maxSingleSide: undefined as number | undefined, //单边最大长度限制 例如：挪威最长边≤45CM
  maxTotalDimension: undefined as number | undefined, //三边和限制（长+宽+高）  例如：挪威长+宽+高≤90CM，土耳其长+宽+高≤90cm
  maxLengthPlusDoubleGirth: undefined as number | undefined, //长 + 2*(宽+高) 限制 例如：加拿大长+2*(宽+高)≤250cm
  maxSecondLongestSide: undefined as number | undefined, //第二长边限制 例如：加拿大第二边长≤76cm
  oversizeFee: undefined as number | undefined //超尺寸附加费（人民币分）例如：澳大利亚超尺寸附加费150RMB/票
})
const formData = ref({
  id: undefined as number | undefined,
  productId: undefined as number | undefined,
  countryCodes: [] as string[],
  zoneCode: undefined,
  transitTime: undefined,
  timelinessInfo: undefined as string | undefined,
  chargeType: 'WEIGHT',
  priceType: 'INCREMENTAL',
  firstUnit: 0,
  firstPrice: 0,
  additionalUnit: 0,
  additionalPrice: 0,
  minWeight: 0,
  maxWeight: 0,
  roundingUnit: 100 as number | undefined, // 进位制(g)，默认100g
  minChargeWeight: 0 as number | undefined, // 最低计费重(g)
  priceConfig: undefined as string | undefined, // 统一的价格配置字段
  sizeRestrictions: undefined as string | undefined,
  fuelFeeRate: 0,
  registrationFee: 0,
  operationFee: 0,
  serviceFee: 0,
  customsFee: 0,
  prepayTariff: false,
  tariffRate: 0,
  discountRate: 1,
  effectiveTime: undefined,
  expireTime: undefined,
  sort: 1 as number | undefined,
  status: 0,
  minDeclareValue: 0,
  maxDeclareValue: 0,
  deliveryRate: 0,
  volumeBase: 0
})
const formRules = reactive({
  productId: [{ required: true, message: '产品编号不能为空', trigger: 'blur' }],
  countryCodes: [{ required: true, message: '请至少选择一个国家', trigger: 'change' }],
  chargeType: [
    {
      required: true,
      message: '计费方式不能为空',
      trigger: 'change'
    },
    {
      validator: (_rule: any, value: string, callback: Function) => {
        if (value && !['WEIGHT', 'VOLUME', 'PIECE'].includes(value)) {
          callback(new Error('计费方式只能是WEIGHT、VOLUME、PIECE之一'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  priceType: [
    {
      required: true,
      message: '价格类型不能为空',
      trigger: 'change'
    },
    {
      validator: (_rule: any, value: string, callback: Function) => {
        if (value && !['TIERED', 'INCREMENTAL', 'TIERED_INCREMENTAL'].includes(value)) {
          callback(new Error('价格类型只能是TIERED、INCREMENTAL、TIERED_INCREMENTAL之一'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  // firstUnit: [
  //   {
  //     required: true,
  //     message: '首重/首件数量不能为空',
  //     trigger: 'blur'
  //   },
  //   {
  //     validator: (_rule: any, value: number, callback: Function) => {
  //       if (value !== undefined && value !== null && value < 0) {
  //         console.log('22首重/首件数量必须大于0:', value)
  //         callback(new Error('首重/首件数量必须大于0'))
  //       } else {
  //         callback()
  //       }
  //     },
  //     trigger: 'blur'
  //   }
  // ],
  // firstPrice: [
  //   {
  //     required: true,
  //     message: '首重/首件价格不能为空',
  //     trigger: 'blur'
  //   },
  //   {
  //     validator: (_rule: any, value: number, callback: Function) => {
  //       if (value !== undefined && value !== null && value < 0) {
  //         callback(new Error('首重/首件价格必须大于0'))
  //       } else {
  //         callback()
  //       }
  //     },
  //     trigger: 'blur'
  //   }
  // ],
  additionalUnit: [
    {
      validator: (_rule: any, value: number, callback: Function) => {
        // 只有在递增计费模式下才验证
        if (formData.value.priceType === 'INCREMENTAL') {
          if (value === undefined || value === null || value < 0) {
            callback(new Error('递增计费模式下续重/续件单位必须大于0'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  additionalPrice: [
    {
      validator: (_rule: any, value: number, callback: Function) => {
        // 只有在递增计费模式下才验证
        if (formData.value.priceType === 'INCREMENTAL') {
          if (value === undefined || value === null || value < 0) {
            callback(new Error('递增计费模式下续重/续件价格必须大于0'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  priceConfig: [
    {
      validator: (_rule: any, value: string, callback: Function) => {
        // 只有在阶梯计费或混合计费模式下才验证
        if (formData.value.priceType === 'TIERED') {
          if (!value || value.trim() === '') {
            // 检查阶梯配置是否为空
            if (tieredConfig.value.length === 0) {
              callback(new Error('阶梯计费模式下价格配置不能为空'))
              return
            }
          }

          // 验证JSON格式
          if (value && value.trim() !== '') {
            try {
              const parsed = JSON.parse(value)
              if (!Array.isArray(parsed)) {
                callback(new Error('价格配置必须是数组格式'))
                return
              }
              // 验证阶梯配置的完整性
              for (let i = 0; i < parsed.length; i++) {
                const item = parsed[i]
                if (!item.tierStart && item.tierStart !== 0) {
                  callback(new Error(`第${i + 1}个阶梯的起始重量不能为空`))
                  return
                }
                if (!item.tierEnd && item.tierEnd !== 0) {
                  callback(new Error(`第${i + 1}个阶梯的结束重量不能为空`))
                  return
                }
                if (!item.unitPrice && item.unitPrice !== 0) {
                  callback(new Error(`第${i + 1}个阶梯的单价不能为空`))
                  return
                }
                if (item.tierStart < 0) {
                  callback(new Error(`第${i + 1}个阶梯的起始重量不能小于0`))
                  return
                }
                if (item.tierEnd <= item.tierStart) {
                  callback(new Error(`第${i + 1}个阶梯的结束重量必须大于起始重量`))
                  return
                }
                if (item.unitPrice <= 0) {
                  callback(new Error(`第${i + 1}个阶梯的单价必须大于0`))
                  return
                }
                // 验证新增字段
                if (item.roundingUnit !== undefined && item.roundingUnit <= 0) {
                  callback(new Error(`第${i + 1}个阶梯的进位制必须大于0`))
                  return
                }
                if (item.minChargeWeight !== undefined && item.minChargeWeight < 0) {
                  callback(new Error(`第${i + 1}个阶梯的最低计费重不能小于0`))
                  return
                }
              }
            } catch (e) {
              callback(new Error('价格配置JSON格式错误'))
              return
            }
          }
        } else if (formData.value.priceType === 'TIERED_INCREMENTAL') {
          if (!value || value.trim() === '') {
            // 检查混合计费配置是否为空
            if (tieredIncrementalConfig.value.length === 0) {
              callback(new Error('混合计费模式下价格配置不能为空'))
              return
            }
          }
        }
        callback()
      },
      trigger: 'blur'
    }
  ],
  status: [{ required: true, message: '开启状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, productId: number, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  formData.value.productId = productId
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const priceData = await LogisticsProductApi.getLogisticsProductPrice(id)
      formData.value = {
        ...priceData,
        countryCodes: priceData.countryCode ? [priceData.countryCode] : []
      }

      // 设置选中的国家（编辑时只有一个国家）
      if (priceData.countryCode) {
        const countryList = await CountryApi.getCountrySimpleList()
        const country = countryList.find((item: CountryItem) => item.code === priceData.countryCode)
        if (country) {
          selectedCountries.value = [country]
        }
      }

      // 解析时效分布配置
      if (formData.value.timelinessInfo) {
        try {
          const parsed = JSON.parse(formData.value.timelinessInfo)
          timelinessConfig.value = {
            deliveryRate: parsed.deliveryRate || undefined,
            timelinessInfos: parsed.timelinessInfos || []
          }
        } catch (e) {
          console.warn('解析时效分布配置失败:', e)
        }
      }

      // 解析价格配置（统一使用priceConfig字段）
      if (formData.value.priceConfig) {
        try {
          const parsed = JSON.parse(formData.value.priceConfig)

          // 根据价格类型解析不同的配置
          if (formData.value.priceType === 'TIERED' && Array.isArray(parsed)) {
            // 阶梯计费配置
            tieredConfig.value = parsed.map((item) => ({
              tierStart: item.tierStart || 0,
              tierEnd: item.tierEnd || 0,
              unitPrice: item.unitPrice || 0,
              registrationFee: item.registrationFee || undefined,
              roundingUnit: item.roundingUnit || undefined,
              minChargeWeight: item.minChargeWeight || undefined
            }))
          } else if (formData.value.priceType === 'TIERED_INCREMENTAL' && Array.isArray(parsed)) {
            // 混合计费配置
            tieredIncrementalConfig.value = parsed.map((item) => ({
              tierStart: item.tierStart || 0,
              tierEnd: item.tierEnd || 0,
              firstWeight: item.firstWeight || 100,
              firstPrice: item.firstPrice || 0,
              additionalWeight: item.additionalWeight || 100,
              additionalPrice: item.additionalPrice || 0,
              registrationFee: item.registrationFee || undefined,
              roundingUnit: item.roundingUnit || undefined,
              minChargeWeight: item.minChargeWeight || undefined
            }))
          }
        } catch (e) {
          console.warn('解析价格配置失败:', e)
        }
      }

      // 解析尺寸限制配置
      if (formData.value.sizeRestrictions) {
        try {
          const parsed = JSON.parse(formData.value.sizeRestrictions)
          sizeRestrictionsConfig.value = {
            // maxLength: parsed.maxLength || undefined,
            // maxWidth: parsed.maxWidth || undefined,
            // maxHeight: parsed.maxHeight || undefined,
            // maxGirth: parsed.maxGirth || undefined,
            // maxSingleSide: parsed.maxSingleSide || undefined

            minLength: parsed.minLength || undefined,
            minWidth: parsed.minWidth || undefined,
            minHeight: parsed.minHeight || undefined,
            maxLength: parsed.maxLength || undefined,
            maxWidth: parsed.maxWidth || undefined,
            maxHeight: parsed.maxHeight || undefined,

            maxSingleSide: parsed.maxSingleSide || undefined, //单边最大长度限制 例如：挪威最长边≤45CM
            maxTotalDimension: parsed.maxTotalDimension || undefined, //三边和限制（长+宽+高）  例如：挪威长+宽+高≤90CM，土耳其长+宽+高≤90cm
            maxLengthPlusDoubleGirth: parsed.maxLengthPlusDoubleGirth || undefined, //长 + 2*(宽+高) 限制 例如：加拿大长+2*(宽+高)≤250cm
            maxSecondLongestSide: parsed.maxSecondLongestSide || undefined, //第二长边限制 例如：加拿大第二边长≤76cm
            oversizeFee: parsed.oversizeFee || undefined //超尺寸附加费（人民币分）例如：澳大利亚超尺寸附加费150RMB/票
          }
        } catch (e) {
          console.warn('解析尺寸限制配置失败:', e)
        }
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 时效分布配置相关方法 */
const addTimelinessItem = () => {
  timelinessConfig.value.timelinessInfos.push({
    timeInterval: '',
    rate: 0
  })
}

const removeTimelinessItem = (index: number) => {
  timelinessConfig.value.timelinessInfos.splice(index, 1)
}

/** 阶梯价格配置相关方法 */
const addTieredItem = () => {
  tieredConfig.value.push({
    tierStart: 0,
    tierEnd: 0,
    unitPrice: 0,
    registrationFee: undefined, // 挂号费默认为空，使用主配置
    roundingUnit: undefined, // 进位制默认为空，使用主配置
    minChargeWeight: undefined // 最低计费重默认为空，使用主配置
  })
}

const removeTieredItem = (index: number) => {
  tieredConfig.value.splice(index, 1)
}

/** 混合计费配置相关方法 */
const addTieredIncrementalItem = () => {
  tieredIncrementalConfig.value.push({
    tierStart: 0,
    tierEnd: 0,
    firstWeight: 100,
    firstPrice: 0,
    additionalWeight: 100,
    additionalPrice: 0,
    registrationFee: undefined, // 挂号费默认为空，使用主配置
    roundingUnit: undefined, // 进位制默认为空，使用主配置
    minChargeWeight: undefined // 最低计费重默认为空，使用主配置
  })
}

const removeTieredIncrementalItem = (index: number) => {
  tieredIncrementalConfig.value.splice(index, 1)
}

/** 业务逻辑验证 */
const validateBusinessLogic = () => {
  // 验证计费方式
  if (!formData.value.chargeType) {
    throw new Error('计费方式不能为空')
  }
  if (!['WEIGHT', 'VOLUME', 'PIECE'].includes(formData.value.chargeType)) {
    throw new Error('计费方式只能是WEIGHT、VOLUME、PIECE之一')
  }

  // 验证价格类型
  if (!formData.value.priceType) {
    throw new Error('价格类型不能为空')
  }
  if (!['TIERED', 'INCREMENTAL', 'TIERED_INCREMENTAL'].includes(formData.value.priceType)) {
    throw new Error('价格类型只能是TIERED、INCREMENTAL、TIERED_INCREMENTAL之一')
  }

  // 验证首重/首件
  // if (formData.value.firstUnit <= 0) {
  //   throw new Error('首重/首件数量必须大于0')
  // }
  // if (formData.value.firstPrice <= 0) {
  //   throw new Error('首重/首件价格必须大于0')
  // }

  // 递增计费模式验证
  if (formData.value.priceType === 'INCREMENTAL') {
    if (!formData.value.additionalUnit || formData.value.additionalUnit < 0) {
      throw new Error('递增计费模式下续重/续件单位必须大于0')
    }
    if (!formData.value.additionalPrice || formData.value.additionalPrice < 0) {
      throw new Error('递增计费模式下续重/续件价格必须大于0')
    }
  }

  // 阶梯计费模式验证
  if (formData.value.priceType === 'TIERED') {
    if (tieredConfig.value.length === 0) {
      throw new Error('阶梯计费模式下价格配置不能为空')
    }

    // 验证每个阶梯的完整性
    for (let i = 0; i < tieredConfig.value.length; i++) {
      const item = tieredConfig.value[i]
      if (item.tierStart === undefined || item.tierStart === null) {
        throw new Error(`第${i + 1}个阶梯的起始重量不能为空`)
      }
      if (item.tierEnd === undefined || item.tierEnd === null) {
        throw new Error(`第${i + 1}个阶梯的结束重量不能为空`)
      }
      if (item.unitPrice === undefined || item.unitPrice === null) {
        throw new Error(`第${i + 1}个阶梯的单价不能为空`)
      }
      if (item.tierStart < 0) {
        throw new Error(`第${i + 1}个阶梯的起始重量不能小于0`)
      }
      if (item.tierEnd <= item.tierStart) {
        throw new Error(`第${i + 1}个阶梯的结束重量必须大于起始重量`)
      }
      if (item.unitPrice <= 0) {
        throw new Error(`第${i + 1}个阶梯的单价必须大于0`)
      }
      // 验证新增字段
      if (item.roundingUnit !== undefined && item.roundingUnit <= 0) {
        throw new Error(`第${i + 1}个阶梯的进位制必须大于0`)
      }
      if (item.minChargeWeight !== undefined && item.minChargeWeight < 0) {
        throw new Error(`第${i + 1}个阶梯的最低计费重不能小于0`)
      }
    }

    // 验证阶梯的连续性
    const sortedTiers = [...tieredConfig.value].sort((a, b) => a.tierStart - b.tierStart)
    for (let i = 0; i < sortedTiers.length - 1; i++) {
      if (sortedTiers[i].tierEnd !== sortedTiers[i + 1].tierStart - 1) {
        throw new Error(
          `阶梯配置不连续：第${i + 1}个阶梯的结束重量(${sortedTiers[i].tierEnd})与第${i + 2}个阶梯的起始重量(${sortedTiers[i + 1].tierStart})不匹配`
        )
      }
    }
  }

  // 混合计费模式验证
  if (formData.value.priceType === 'TIERED_INCREMENTAL') {
    if (tieredIncrementalConfig.value.length === 0) {
      throw new Error('混合计费模式下价格配置不能为空')
    }

    // 验证每个混合计费项的完整性
    for (let i = 0; i < tieredIncrementalConfig.value.length; i++) {
      const item = tieredIncrementalConfig.value[i]
      if (item.tierStart === undefined || item.tierStart === null) {
        throw new Error(`第${i + 1}个阶梯的起始重量不能为空`)
      }
      if (item.tierEnd === undefined || item.tierEnd === null) {
        throw new Error(`第${i + 1}个阶梯的结束重量不能为空`)
      }
      if (item.firstWeight === undefined || item.firstWeight === null || item.firstWeight <= 0) {
        throw new Error(`第${i + 1}个阶梯的首重必须大于0`)
      }
      if (item.firstPrice === undefined || item.firstPrice === null || item.firstPrice <= 0) {
        throw new Error(`第${i + 1}个阶梯的首重价格必须大于0`)
      }
      if (
        item.additionalWeight === undefined ||
        item.additionalWeight === null ||
        item.additionalWeight <= 0
      ) {
        throw new Error(`第${i + 1}个阶梯的续重必须大于0`)
      }
      if (
        item.additionalPrice === undefined ||
        item.additionalPrice === null ||
        item.additionalPrice <= 0
      ) {
        throw new Error(`第${i + 1}个阶梯的续重价格必须大于0`)
      }
      // 验证新增字段
      if (item.roundingUnit !== undefined && item.roundingUnit <= 0) {
        throw new Error(`第${i + 1}个阶梯的进位制必须大于0`)
      }
      if (item.minChargeWeight !== undefined && item.minChargeWeight < 0) {
        throw new Error(`第${i + 1}个阶梯的最低计费重不能小于0`)
      }
    }
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  try {
    await formRef.value.validate()
  } catch (error) {
    message.error('请检查表单填写是否正确')
    return
  }

  // 额外的业务逻辑验证
  try {
    validateBusinessLogic()
  } catch (error: any) {
    message.error(error.message || '数据验证失败')
    return
  }

  // 提交请求
  formLoading.value = true
  try {
    const data = { ...formData.value }

    // 转换时效分布配置为JSON
    if (
      timelinessConfig.value.deliveryRate !== undefined ||
      timelinessConfig.value.timelinessInfos.length > 0
    ) {
      data.timelinessInfo = JSON.stringify(timelinessConfig.value)
    }

    // 转换价格配置为JSON（统一使用priceConfig字段）
    if (formData.value.priceType === 'TIERED' && tieredConfig.value.length > 0) {
      // 过滤掉undefined的字段
      const cleanedTieredConfig = tieredConfig.value.map((item) => {
        const cleanedItem: any = {
          tierStart: item.tierStart,
          tierEnd: item.tierEnd,
          unitPrice: item.unitPrice
        }
        // 只有当字段有值时才添加到JSON中
        if (item.registrationFee !== undefined && item.registrationFee !== null) {
          cleanedItem.registrationFee = item.registrationFee
        }
        if (item.roundingUnit !== undefined && item.roundingUnit !== null) {
          cleanedItem.roundingUnit = item.roundingUnit
        }
        if (item.minChargeWeight !== undefined && item.minChargeWeight !== null) {
          cleanedItem.minChargeWeight = item.minChargeWeight
        }
        return cleanedItem
      })
      data.priceConfig = JSON.stringify(cleanedTieredConfig)
    }

    if (
      formData.value.priceType === 'TIERED_INCREMENTAL' &&
      tieredIncrementalConfig.value.length > 0
    ) {
      // 过滤掉undefined的字段
      const cleanedTieredIncrementalConfig = tieredIncrementalConfig.value.map((item) => {
        const cleanedItem: any = {
          tierStart: item.tierStart,
          tierEnd: item.tierEnd,
          firstWeight: item.firstWeight,
          firstPrice: item.firstPrice,
          additionalWeight: item.additionalWeight,
          additionalPrice: item.additionalPrice
        }
        // 只有当字段有值时才添加到JSON中
        if (item.registrationFee !== undefined && item.registrationFee !== null) {
          cleanedItem.registrationFee = item.registrationFee
        }
        if (item.roundingUnit !== undefined && item.roundingUnit !== null) {
          cleanedItem.roundingUnit = item.roundingUnit
        }
        if (item.minChargeWeight !== undefined && item.minChargeWeight !== null) {
          cleanedItem.minChargeWeight = item.minChargeWeight
        }
        return cleanedItem
      })
      data.priceConfig = JSON.stringify(cleanedTieredIncrementalConfig)
    }

    // 转换尺寸限制配置为JSON
    const hasValidSizeRestriction = Object.values(sizeRestrictionsConfig.value).some(
      (value) => value !== undefined && value !== null
    )
    if (hasValidSizeRestriction) {
      // 过滤掉undefined和null的值
      const validSizeRestrictions = Object.fromEntries(
        Object.entries(sizeRestrictionsConfig.value).filter(
          ([_, value]) => value !== undefined && value !== null
        )
      )
      data.sizeRestrictions = JSON.stringify(validSizeRestrictions)
    }
    if (formType.value === 'create') {
      // 新增时使用批量创建接口
      if (data.countryCodes && data.countryCodes.length > 0) {
        await LogisticsProductApi.createLogisticsProductPrice(data)
        message.success(`成功创建 ${data.countryCodes.length} 个国家的价格规则`)
      } else {
        message.error('请至少选择一个国家')
        return
      }
    } else {
      // 编辑时转换回单个国家编码
      const { countryCodes, ...updateData } = data
      const finalData = {
        ...updateData,
        countryCode: countryCodes && countryCodes.length > 0 ? countryCodes[0] : undefined
      }
      await LogisticsProductApi.updateLogisticsProductPrice(finalData)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 国家选择相关方法 */
const openCountryDialog = () => {
  const selectedCodes = selectedCountries.value.map((country) => country.code)
  countrySelectRef.value?.open(selectedCodes)
}

const handleCountrySelect = (countries: CountryItem[]) => {
  selectedCountries.value = countries
  formData.value.countryCodes = countries.map((country) => country.code)
}

const removeCountry = (code: string) => {
  const index = selectedCountries.value.findIndex((country) => country.code === code)
  if (index > -1) {
    selectedCountries.value.splice(index, 1)
    formData.value.countryCodes = selectedCountries.value.map((country) => country.code)
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    productId: undefined,
    countryCodes: [],
    zoneCode: undefined,
    transitTime: undefined,
    timelinessInfo: undefined,
    chargeType: 'WEIGHT',
    priceType: 'INCREMENTAL',
    firstUnit: 0,
    firstPrice: 0,
    additionalUnit: 0,
    additionalPrice: 0,
    minWeight: 0,
    maxWeight: 0,
    roundingUnit: 100,
    minChargeWeight: 0,
    priceConfig: undefined,
    sizeRestrictions: undefined,
    fuelFeeRate: 0,
    registrationFee: 0,
    operationFee: 0,
    serviceFee: 0,
    customsFee: 0,
    prepayTariff: false,
    tariffRate: 0,
    discountRate: 1,
    effectiveTime: undefined,
    expireTime: undefined,
    sort: 1,
    status: 0,
    minDeclareValue: 0,
    maxDeclareValue: 0,
    deliveryRate: 0,
    volumeBase: 0
  }

  // 重置时效分布配置
  timelinessConfig.value = {
    deliveryRate: 98.0,
    timelinessInfos: []
  }

  // 重置阶梯价格配置
  tieredConfig.value = []

  // 重置混合计费配置
  tieredIncrementalConfig.value = []

  // 重置尺寸限制配置
  sizeRestrictionsConfig.value = {
    // maxLength: undefined,
    // maxWidth: undefined,
    // maxHeight: undefined,
    // maxGirth: undefined,
    // maxSingleSide: undefined

    minLength: undefined,
    minWidth: undefined,
    minHeight: undefined,
    maxLength: undefined,
    maxWidth: undefined,
    maxHeight: undefined,

    maxSingleSide: undefined, //单边最大长度限制 例如：挪威最长边≤45CM
    maxTotalDimension: undefined, //三边和限制（长+宽+高）  例如：挪威长+宽+高≤90CM，土耳其长+宽+高≤90cm
    maxLengthPlusDoubleGirth: undefined, //长 + 2*(宽+高) 限制 例如：加拿大长+2*(宽+高)≤250cm
    maxSecondLongestSide: undefined, //第二长边限制 例如：加拿大第二边长≤76cm
    oversizeFee: undefined //超尺寸附加费（人民币分）例如：澳大利亚超尺寸附加费150RMB/票
  }

  // 重置选中的国家
  selectedCountries.value = []

  formRef.value?.resetFields()
}

// 监听价格类型变化，触发相关字段验证
watch(
  () => formData.value.priceType,
  (newType) => {
    // 当价格类型改变时，清除相关字段的验证错误
    nextTick(() => {
      if (newType === 'INCREMENTAL') {
        // 切换到递增模式时，验证续重字段
        formRef.value?.validateField(['additionalUnit', 'additionalPrice'])
      } else if (newType === 'TIERED' || newType === 'TIERED_INCREMENTAL') {
        // 切换到阶梯模式或混合计费模式时，验证价格配置
        formRef.value?.validateField(['priceConfig'])
      }
    })
  }
)

// 监听阶梯配置变化，触发验证
watch(
  () => tieredConfig.value,
  () => {
    if (formData.value.priceType === 'TIERED') {
      nextTick(() => {
        formRef.value?.validateField(['priceConfig'])
      })
    }
  },
  { deep: true }
)

// 监听混合计费配置变化，触发验证
watch(
  () => tieredIncrementalConfig.value,
  () => {
    if (formData.value.priceType === 'TIERED_INCREMENTAL') {
      nextTick(() => {
        formRef.value?.validateField(['priceConfig'])
      })
    }
  },
  { deep: true }
)
</script>

<style scoped>
/* 阶梯价格配置整体布局 */
.tiered-config {
  background-color: #f9f9f9;
}

/* 阶梯价格行布局 */
.tiered-row {
  display: flex;
  align-items: flex-end; /* 让所有元素底部对齐 */
  /* min-height: 60px; 确保有足够的高度 */
}

/* 阶梯价格列布局 */
.tiered-col {
  display: flex;
  flex-direction: column;
  justify-content: flex-end; /* 内容向底部对齐 */
}

/* 表单项样式 */
.tiered-form-item {
  margin-bottom: 0 !important;
}

/* 表单项标签样式 */
.tiered-form-item :deep(.el-form-item__label) {
  padding-bottom: 8px;
  /* line-height: 1.2; */
  font-size: 12px;
}

/* 输入框样式 - 使用更强的选择器 */
.tiered-config .tiered-input {
  width: 110px !important;
  min-width: 110px !important;
}

.tiered-config .tiered-input :deep(.el-input__wrapper) {
  width: 100% !important;
}

.tiered-config .tiered-input :deep(.el-input) {
  width: 100% !important;
}

/* 确保Element Plus组件内部样式也被覆盖 */
.tiered-config .el-input-number {
  width: 130px !important;
  min-width: 130px !important;
}

/* 按钮列特殊处理 */
.tiered-button-col {
  /* display: flex; */
  /* align-items: flex-end; */
  /* padding-bottom: 0; */
}

/* 删除按钮样式 */
.tiered-delete-btn {
  margin-bottom: 0;
  height: 32px; /* 与输入框高度保持一致 */
}

/* 确保输入框和按钮在同一水平线 */
.tiered-row .el-input-number,
.tiered-row .el-button {
  vertical-align: bottom;
}
</style>
