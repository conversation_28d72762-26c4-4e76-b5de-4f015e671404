<template>
  <div class="h-full flex flex-col">
    <!-- 没有选择产品时的提示 -->
    <div v-if="!props.productId" class="flex-1 flex items-center justify-center">
      <div class="text-center text-gray-500">
        <Icon icon="ep:box" class="text-4xl mb-2" />
        <div class="text-lg mb-1">请先选择物流产品</div>
        <div class="text-sm">选择左侧列表中的产品以查看价格信息</div>
      </div>
    </div>

    <!-- 简化的表格，适应右侧布局 -->
    <el-table
      v-else
      v-loading="loading"
      :data="list"
      :show-overflow-tooltip="true"
      :height="height || 'auto'"
      :row-class-name="tableRowClassName"
    >
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="国家编码" align="center" prop="countryCode" />
      <el-table-column label="分区编码" align="center" prop="zoneCode" />
      <el-table-column label="时效" align="center" prop="transitTime" />
      <el-table-column label="计费方式" align="center" prop="chargeType" />
      <el-table-column label="首重价格(分)" align="center" prop="firstPrice" />
      <el-table-column label="续重价格(分)" align="center" prop="additionalPrice" />
      <el-table-column label="开启状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="170" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handlePriceChangeState(scope.row)"
            v-hasPermi="['agent:logistics-product:update']"
          >
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['agent:logistics-product:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['agent:logistics-product:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="mt-4 flex-shrink-0" v-if="total > 0">
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        layout="prev, pager, next"
        :page-sizes="[10, 20, 50]"
      />
    </div>

    <!-- 无数据提示 -->
    <div v-if="!loading && list.length === 0" class="text-center text-gray-500 py-8">
      暂无价格规则数据
    </div>
  </div>

  <!-- 表单弹窗：添加/修改 -->
  <LogisticsProductPriceForm ref="formRef" @success="getList" />
</template>
<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { LogisticsProductApi } from '@/api/mall/agent/logisticsProduct'
import LogisticsProductPriceForm from './LogisticsProductPriceForm.vue'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const props = defineProps<{
  productId?: number // 产品编号（主表的关联字段）
  height?: number | string // 表格高度
}>()
const loading = ref(false) // 列表的加载中
const list = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  productId: undefined as unknown
})

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.productId,
  (val: number) => {
    if (!val) {
      return
    }
    queryParams.productId = val
    handleQuery()
  },
  { immediate: true, deep: true }
)

/** 查询列表 */
const getList = async () => {
  // 如果没有 productId，不发送请求
  if (!queryParams.productId) {
    list.value = []
    total.value = 0
    return
  }

  loading.value = true
  try {
    const data = await LogisticsProductApi.getLogisticsProductPricePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const tableRowClassName = ({ row }) => {
  if (row.status === 1) {
    return 'disabled-row'
  }
  return ''
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  if (!props.productId) {
    message.error('请选择一个代购物流公司产品')
    return
  }
  formRef.value.open(type, props.productId, id)
}

const handlePriceChangeState = async (row: any) => {
  const state = row.status
  const newState = state === 1 ? 0 : 1
  try {
    // 修改状态的二次确认
    const id = row.id
    debugger
    const statusState = state === 0 ? '停用' : '启用'
    const content = '是否确认' + statusState + '编号为"' + row.id + '"的数据项?'
    await message.confirm(content)
    // 发起修改状态
    await LogisticsProductApi.updateLogisticsProductPriceStatus(id, newState)
    message.success(statusState + '成功')
    // 刷新列表
    await getList()
  } catch {}
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await LogisticsProductApi.deleteLogisticsProductPrice(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 暴露方法给父组件 */
defineExpose({
  getList,
  openForm
})

onMounted(async () => {
  // 只有在有 productId 时才加载数据
  if (props.productId) {
    await getList()
  }
})
</script>

<!-- <style lang="scss" scoped>
::v-deep .disabled-row {
  background-color: #fc3239 !important;
}
</style> -->
