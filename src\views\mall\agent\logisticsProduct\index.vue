<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="公司编号" prop="companyId">
        <el-input
          v-model="queryParams.companyId"
          placeholder="请输入物流公司编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="产品编码" prop="productCode">
        <el-input
          v-model="queryParams.productCode"
          placeholder="请输入产品编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="中文名称" prop="nameZh">
        <el-input
          v-model="queryParams.nameZh"
          placeholder="请输入中文名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="英文名称" prop="nameEn">
        <el-input
          v-model="queryParams.nameEn"
          placeholder="请输入英文名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['agent:logistics-product:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['agent:logistics-product:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 主从表左右分栏布局 -->
  <ContentWrap>
    <div class="flex gap-4 min-h-[800px]">
      <!-- 左侧：主表列表 -->
      <div class="w-150 flex-shrink-0">
        <div class="bg-white border rounded-lg p-4 h-full">
          <el-table
            v-loading="loading"
            :data="list"
            :show-overflow-tooltip="true"
            highlight-current-row
            @current-change="handleCurrentChange"
            :height="isMobile ? 300 : 900"
            :row-class-name="tableRowClassName"
          >
            <el-table-column label="编号" align="left" prop="id" width="55" />
            <el-table-column label="中文名称" align="left" prop="nameZh" min-width="180" />
            <el-table-column label="产品编码" align="left" prop="productCode" min-width="80" />
            <el-table-column label="操作" align="left" width="170">
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  @click="handleChangeState(scope.row)"
                  v-hasPermi="['agent:logistics-product:update']"
                >
                  {{ scope.row.status === 1 ? '启用' : '禁用' }}
                </el-button>
                <el-button
                  link
                  type="primary"
                  @click="openForm('update', scope.row.id)"
                  v-hasPermi="['agent:logistics-product:update']"
                >
                  编辑
                </el-button>
                <el-button
                  link
                  type="danger"
                  @click="handleDelete(scope.row.id)"
                  v-hasPermi="['agent:logistics-product:delete']"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="mt-4 flex justify-center">
            <!-- 分页 -->
            <Pagination
              :total="total"
              v-model:page="queryParams.pageNo"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
              :page-sizes="[10, 20, 50, 100]"
            />
          </div>
        </div>
      </div>

      <!-- 右侧：主表详情和子表 -->
      <div class="flex-1 min-w-0">
        <div class="h-full flex flex-col gap-4">
          <!-- 右侧上半部分：主表详情 -->
          <div class="bg-white border rounded-lg p-4 flex-shrink-0">
            <div v-if="currentRow && currentRow.id" class="min-h-[300px]">
              <!-- 操作按钮 -->
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium">产品详情</h3>
                <!-- <div class="flex gap-2">
                  <el-button
                    type="primary"
                    @click="openForm('update', currentRow.id)"
                    v-hasPermi="['agent:logistics-product:update']"
                  >
                    <Icon icon="ep:edit" class="mr-1" /> 编辑
                  </el-button>
                  <el-button
                    type="danger"
                    @click="handleDelete(currentRow.id)"
                    v-hasPermi="['agent:logistics-product:delete']"
                  >
                    <Icon icon="ep:delete" class="mr-1" /> 删除
                  </el-button>
                </div> -->
              </div>

              <!-- 详情信息 -->
              <div v-if="currentRow && currentRow.id" class="grid grid-cols-4 xl:grid-cols-5 gap-3">
                <div class="detail-item">
                  <div class="detail-label">编号</div>
                  <div class="detail-value">{{ currentRow.id }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">公司编号</div>
                  <div class="detail-value">{{ currentRow.companyId }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">产品编码</div>
                  <div class="detail-value">{{ currentRow.productCode }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">渠道编码</div>
                  <div class="detail-value">{{ currentRow.channelCode }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">状态</div>
                  <div class="detail-value">
                    <el-tag :type="currentRow.status === 0 ? 'success' : 'danger'" size="small">
                      {{ currentRow.status === 0 ? '启用' : '禁用' }}
                    </el-tag>
                  </div>
                </div>

                <div class="detail-item">
                  <div class="detail-label">中文名称</div>
                  <div class="detail-value">{{ currentRow.nameZh }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">英文名称</div>
                  <div class="detail-value">{{ currentRow.nameEn }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">是否包税</div>
                  <div class="detail-value">{{ currentRow.taxInclude ? '是' : '否' }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">计算体积重</div>
                  <div class="detail-value">{{ currentRow.needVolumeCal ? '是' : '否' }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">体积重基数</div>
                  <div class="detail-value">{{ currentRow.volumeBase }}</div>
                </div>

                <div class="detail-item">
                  <div class="detail-label">最小重量(g)</div>
                  <div class="detail-value">{{ currentRow.minWeight }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">最大重量(g)</div>
                  <div class="detail-value">{{ currentRow.maxWeight }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">免费保险</div>
                  <div class="detail-value">{{ currentRow.freeInsure ? '是' : '否' }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">排序</div>
                  <div class="detail-value">{{ currentRow.sort }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">创建时间</div>
                  <div class="detail-value">{{ formatDate((currentRow as any).createTime) }}</div>
                </div>

                <!-- 长文本字段单独一行 -->
                <!-- <div class="detail-item col-span-4 xl:col-span-5" v-if="currentRow.featuresZh">
                  <div class="detail-label">特性描述</div>
                  <div class="detail-value">{{ currentRow.featuresZh }}</div>
                </div>
                <div
                  class="detail-item col-span-4 xl:col-span-5"
                  v-if="currentRow.dimensionRestrictionZh"
                >
                  <div class="detail-label">尺寸限制</div>
                  <div class="detail-value">{{ currentRow.dimensionRestrictionZh }}</div>
                </div>
                <div
                  class="detail-item col-span-4 xl:col-span-5"
                  v-if="currentRow.volumeWeightRuleZh"
                >
                  <div class="detail-label">体积重量规则描述</div>
                  <div class="detail-value">{{ currentRow.volumeWeightRuleZh }}</div>
                </div> -->
              </div>
            </div>
            <div v-else class="text-center py-8 min-h-[300px]">
              <Icon icon="ep:info-filled" class="text-4xl text-gray-400 mb-2" />
              <div class="text-gray-500">请从左侧列表选择一条记录查看详情</div>
            </div>

            <!-- 右侧下半部分：Tab信息 -->
            <div class="bg-white border rounded-lg p-4 flex-1">
              <!-- Tab 切换 -->
              <el-tabs v-model="activeTab" class="h-full">
                <el-tab-pane label="价格信息" name="price">
                  <!-- 价格操作区域 -->
                  <el-form class="-mb-15px" :inline="true" label-width="68px">
                    <el-form-item>
                      <el-button
                        type="primary"
                        plain
                        @click="openPriceForm('create')"
                        v-hasPermi="['agent:logistics-product:create']"
                        :disabled="!currentRow || !currentRow.id"
                      >
                        <Icon icon="ep:plus" class="mr-5px" /> 新增
                      </el-button>
                      <el-button
                        type="success"
                        plain
                        @click="downloadTemplate"
                        v-hasPermi="['agent:logistics-product:import']"
                      >
                        <Icon icon="ep:download" class="mr-5px" /> 下载模板
                      </el-button>
                      &nbsp;&nbsp;&nbsp;&nbsp;
                      <el-upload
                        ref="uploadRef"
                        :auto-upload="false"
                        :on-change="handleFileChange"
                        :show-file-list="false"
                        accept=".xls,.xlsx"
                        :disabled="!currentRow || !currentRow.id || importLoading"
                      >
                        <el-button
                          type="warning"
                          plain
                          :disabled="!currentRow || !currentRow.id || importLoading"
                          :loading="importLoading"
                          v-hasPermi="['agent:logistics-product:import']"
                        >
                          <Icon icon="ep:upload" class="mr-5px" />
                          {{ importLoading ? '导入中...' : '导入价格' }}
                        </el-button>
                      </el-upload>
                      <el-checkbox
                        v-model="updateSupport"
                        size="small"
                        :disabled="!currentRow || !currentRow.id"
                        class="ml-2"
                      >
                        支持更新
                      </el-checkbox>
                    </el-form-item>
                  </el-form>

                  <!-- 导入结果显示 -->
                  <div v-if="importResult" class="mb-4">
                    <el-alert
                      :type="importResult.failureCount > 0 ? 'warning' : 'success'"
                      :title="getResultTitle()"
                      :description="getResultDescription()"
                      show-icon
                      :closable="true"
                      @close="importResult = null"
                    />
                  </div>

                  <!-- 价格列表 -->
                  <LogisticsProductPriceList
                    :product-id="currentRow?.id"
                    :height="400"
                    ref="priceListRef"
                  />
                </el-tab-pane>

                <el-tab-pane label="物流分区" name="zone">
                  <!-- 分区列表 -->
                  <LogisticsZoneList :product-id="currentRow?.id" ref="zoneListRef" />
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <LogisticsProductForm ref="formRef" @success="getList" />

  <!-- 价格表单弹窗：添加/修改价格规则 -->
  <LogisticsProductPriceForm ref="priceFormRef" @success="handlePriceFormSuccess" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { LogisticsProductApi, LogisticsProductVO } from '@/api/mall/agent/logisticsProduct'
import LogisticsProductForm from './LogisticsProductForm.vue'
import LogisticsProductPriceList from './components/LogisticsProductPriceList.vue'
import LogisticsProductPriceForm from './components/LogisticsProductPriceForm.vue'
import LogisticsZoneList from './components/LogisticsZoneList.vue'
import { el } from 'element-plus/es/locale'

/** 代购物流公司产品 列表 */
defineOptions({ name: 'LogisticsProduct' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<LogisticsProductVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  companyId: undefined,
  productCode: undefined,
  nameZh: undefined,
  nameEn: undefined,
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 导入功能相关
const uploadRef = ref() // 上传组件引用
const updateSupport = ref(false) // 是否支持更新已存在数据
const importResult = ref<any>(null) // 导入结果
const importLoading = ref(false) // 导入加载状态

// Tab 相关
const activeTab = ref('price') // 当前激活的Tab
const zoneListRef = ref() // 物流分区列表组件引用

// 响应式断点
const isMobile = computed(() => {
  // 简单的移动端判断，可以根据实际需要调整
  return window.innerWidth < 768
})

// 格式化日期
const formatDate = (date: any) => {
  if (!date) return ''
  return new Date(date).toLocaleString()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await LogisticsProductApi.getLogisticsProductPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

const handleChangeState = async (row: any) => {
  const state = row.status
  const newState = state === 1 ? 0 : 1
  try {
    // 修改状态的二次确认
    const id = row.id
    debugger
    const statusState = state === 0 ? '停用' : '启用'
    const content = '是否确认' + statusState + '名字为"' + row.nameZh + '"的数据项?'
    await message.confirm(content)
    // 发起修改状态
    await LogisticsProductApi.updateLogisticsProductStatus(id, newState)
    message.success(statusState + '成功')
    // 刷新列表
    await getList()
  } catch {}
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await LogisticsProductApi.deleteLogisticsProduct(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await LogisticsProductApi.exportLogisticsProduct(queryParams)
    download.excel(data, '代购物流公司产品.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 选中行操作 */
const currentRow = ref<LogisticsProductVO>({} as LogisticsProductVO) // 选中行
const handleCurrentChange = (row: LogisticsProductVO) => {
  currentRow.value = row
}

/** 价格表相关操作 */
const priceListRef = ref()
const priceFormRef = ref()

/** 打开价格表单 */
const openPriceForm = (type: string, id?: number) => {
  if (!currentRow.value || !currentRow.value.id) {
    message.error('请先选择一个物流产品')
    return
  }
  // 调用价格列表组件中的表单打开方法
  if (priceListRef.value && priceListRef.value.openForm) {
    priceListRef.value.openForm(type, id)
  } else {
    // 如果价格列表组件没有暴露 openForm 方法，则直接调用表单
    priceFormRef.value.open(type, currentRow.value.id, id)
  }
}

/** 价格表单操作成功回调 */
const handlePriceFormSuccess = () => {
  // 刷新价格规则列表
  if (priceListRef.value) {
    priceListRef.value.getList()
  }
}

/** 导入功能相关方法 */

/** 下载导入模板 */
const downloadTemplate = async () => {
  try {
    const data = await LogisticsProductApi.downloadImportTemplate()
    download.excel(data, '物流产品价格规则导入模板.xlsx')
    message.success('模板下载成功')
  } catch {
    message.error('模板下载失败')
  }
}

/** 文件选择处理 */
const handleFileChange = async (file: any) => {
  const rawFile = file.raw

  // 文件格式校验
  const isExcel =
    rawFile.type === 'application/vnd.ms-excel' ||
    rawFile.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  if (!isExcel) {
    message.error('只能上传Excel文件!')
    return
  }

  // 文件大小校验
  const isLt10M = rawFile.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return
  }

  // 产品选择校验
  if (!currentRow.value || !currentRow.value.id) {
    message.error('请先选择一个物流产品')
    return
  }

  // 清空之前的导入结果
  importResult.value = null

  try {
    // 确保 productId 是数字类型
    const productId = Number(currentRow.value.id)
    if (!productId || isNaN(productId)) {
      message.error('产品ID无效，请重新选择产品')
      return
    }

    // 开始导入
    importLoading.value = true
    const response = await LogisticsProductApi.importLogisticsProductPrice(
      productId,
      rawFile,
      updateSupport.value
    )

    // 处理导入结果
    importResult.value = response.data
    message.success('导入完成!')

    // 刷新价格规则列表
    if (priceListRef.value) {
      priceListRef.value.getList()
    }
  } catch (error: any) {
    message.error(error.message || '导入失败')
  } finally {
    importLoading.value = false
    // 清空文件选择
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }
  }
}

/** 获取导入结果标题 */
const getResultTitle = () => {
  if (!importResult.value) return ''
  const { createSuccessCount, updateSuccessCount, failureCount } = importResult.value
  return `导入完成：成功创建${createSuccessCount}条，更新${updateSuccessCount}条，失败${failureCount}条`
}

/** 获取导入结果描述 */
const getResultDescription = () => {
  if (!importResult.value || importResult.value.failureCount === 0) return ''
  return importResult.value.failureData
    .map((item: any) => `第${item.row}行：${item.reason}`)
    .join('；')
}

const tableRowClassName = ({ row }) => {
  if (row.status === 1) {
    return 'disabled-row'
  }
  if (row.recommended) {
    return 'recommended-row'
  }
  return ''
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
/* 使用系统样式，最小化自定义 */
.detail-item {
  background: #fafafa;
  border-radius: 4px;
  padding: 8px 10px;
  border: 1px solid #e4e7ed;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.detail-label {
  font-size: 11px;
  color: #909399;
  margin-bottom: 2px;
  line-height: 1.2;
}

.detail-value {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
  line-height: 1.3;
  word-break: break-all;
}

/* 长文本字段样式 */
.detail-item.col-span-4,
.detail-item.col-span-5 {
  min-height: auto;
  padding: 10px 12px;
}

.detail-item.col-span-4 .detail-value,
.detail-item.col-span-5 .detail-value {
  font-size: 14px;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .flex {
    flex-direction: column;
  }

  .w-80 {
    width: 100%;
  }

  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }

  .xl\:grid-cols-5 {
    grid-template-columns: repeat(2, 1fr);
  }

  .col-span-4,
  .col-span-5 {
    grid-column: span 2;
  }
}

@media (max-width: 1280px) {
  .xl\:grid-cols-5 {
    grid-template-columns: repeat(4, 1fr);
  }

  .xl\:col-span-5 {
    grid-column: span 4;
  }
}

::v-deep .disabled-row {
  background-color: #d33c41 !important;
  color: black !important;
}
::v-deep .recommended-row {
  background-color: #b6f866 !important;
  color: black !important;
}
</style>
