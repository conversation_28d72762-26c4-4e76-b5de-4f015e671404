<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="国家编码" prop="countryCode">
        <el-input v-model="formData.countryCode" placeholder="请输入国家编码" />
      </el-form-item>
      <el-form-item label="产品编号" prop="productIds">
        <el-select
          v-model="selectedProductIds"
          placeholder="请选择产品编号（可多选）"
          multiple
          collapse-tags
          collapse-tags-tooltip
          :max-collapse-tags="30"
          class="w-full"
        >
          <el-option
            v-for="item in logisticsProductList"
            :key="item.id"
            :label="`${item.id} (${item.memo})`"
            :value="item.id"
          />
        </el-select>
        <div v-if="selectedProductIds.length > 0" class="text-xs text-gray-500 mt-1">
          已选择 {{ selectedProductIds.length }} 个产品
        </div>
      </el-form-item>

      <el-form-item label="分区编码" prop="zoneCode">
        <el-input v-model="formData.zoneCode" placeholder="NORMAL/REMOTE/FORBIDDEN/1/2/3/4" />
      </el-form-item>
      <el-form-item label="分区名称" prop="zoneName">
        <el-input v-model="formData.zoneName" placeholder="请输入分区名称" />
      </el-form-item>
      <el-form-item label="州/省/自治区" prop="stateProvince">
        <el-input v-model="formData.stateProvince" placeholder="请输入州/省/自治区" />
      </el-form-item>
      <el-form-item label="城市/地区" prop="city">
        <el-input v-model="formData.city" placeholder="请输入城市/地区" />
      </el-form-item>
      <el-form-item label="区/县" prop="district">
        <el-input v-model="formData.district" placeholder="请输入区/县" />
      </el-form-item>
      <el-form-item label="邮编配置" prop="postalCodes">
        <!-- <el-textarea v-model="formData.postalCodes" placeholder="请输入邮编配置" /> -->
        <el-input
          v-model="formData.postalCodes"
          type="textarea"
          :rows="4"
          placeholder="请输入邮编配置"
        />
      </el-form-item>
      <el-form-item label="限制类型" prop="restrictionType">
        <el-select v-model="formData.restrictionType" placeholder="请选择类型">
          <el-option label="正常" value="NORMAL" />
          <el-option label="偏远地区" value="REMOTE_FEE" />
          <el-option label="不可达地区" value="FORBIDDEN" />
        </el-select>
      </el-form-item>
      <!-- ISLAND-岛屿, TERRITORY-领土, MILITARY-军事基地 -->
      <el-form-item label="特殊区域类型" prop="specialAreaType">
        <el-select v-model="formData.specialAreaType" placeholder="请选择特殊区域类型">
          <el-option label="岛屿" value="ISLAND" />
          <el-option label="领土" value="TERRITORY" />
          <el-option label="军事基地" value="MILITARY" />
        </el-select>
      </el-form-item>
      <el-form-item label="区域描述" prop="fullAreaName">
        <el-input v-model="formData.fullAreaName" placeholder="请输入完整区域描述" />
      </el-form-item>
      <el-form-item label="附加费公式" prop="feeFormula">
        <el-input v-model="formData.feeFormula" placeholder="请输入附加费公式" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" :rows="4" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="formData.sort" placeholder="请输入排序" />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { LogisticsZoneApi, LogisticsZoneVO } from '@/api/mall/agent/logisticsZone'
import { LogisticsProductApi } from '@/api/mall/agent/logisticsProduct'
import { el } from 'element-plus/es/locale'

/** 代购物流国家分区 表单 */
defineOptions({ name: 'LogisticsZoneForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined as number | undefined,
  countryCode: undefined as string | undefined,
  productIds: undefined as string | undefined, // 产品编号列表(JSON格式)
  zoneCode: undefined as string | undefined,
  zoneName: undefined as string | undefined,
  postalCodes: undefined as string | undefined, //邮编配置 JSON格式存储邮编范围或列表
  restrictionType: undefined as string | undefined, //限制类型 (FORBIDDEN-禁止配送, REMOTE_FEE-偏远地区费, NORMAL-正常)
  stateProvince: undefined as string | undefined, //一级行政区划 (州/省/自治区等)
  city: undefined as string | undefined, //二级行政区划 (城市/地区等)
  district: undefined as string | undefined, //三级行政区划 (区/县等)
  specialAreaType: undefined as string | undefined, //特殊区域类型 (ISLAND-岛屿, TERRITORY-领土, MILITARY-军事基地等)
  fullAreaName: undefined as string | undefined, //完整区域描述 (用于显示和搜索，如：California Los Angeles)
  feeFormula: undefined as string | undefined, //附加费公式 (如：3*weight_kg,min:48 表示每公斤3元，最低48元)
  remark: undefined as string | undefined,
  sort: undefined as number | undefined,
  status: undefined as number | undefined
})

// 选中的产品ID数组
const selectedProductIds = ref<number[]>([])
const formRules = reactive({
  countryCode: [{ required: true, message: '国家编码不能为空', trigger: 'blur' }],
  productIds: [
    {
      validator: (_rule: any, value: string, callback: Function) => {
        const hasSelection = selectedProductIds.value && selectedProductIds.value.length > 0
        // 如果你想：可为空，不强制选择 → 直接放行
        callback()
        // if (!selectedProductIds.value || selectedProductIds.value.length === 0) {
        //   callback(new Error('请至少选择一个产品'))
        // } else {
        //   callback()
        // }
      },
      trigger: 'change'
    }
  ],
  status: [{ required: true, message: '开启状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

const logisticsProductList = ref<any[]>([])
/** 打开弹窗 */
const open = async (type: string, id?: number, productId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  logisticsProductList.value = await LogisticsProductApi.getSimpleLogisticsProductList()

  // 如果传入了productId，设置到选中的产品列表中
  if (productId) {
    selectedProductIds.value = [productId]
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await LogisticsZoneApi.getLogisticsZone(id)
      formData.value = data

      // 解析productIds字段，设置到选中的产品列表
      if (data.productIds) {
        try {
          selectedProductIds.value = JSON.parse(data.productIds)
        } catch (e) {
          console.warn('解析productIds失败:', e)
          selectedProductIds.value = []
        }
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = { ...formData.value } as unknown as LogisticsZoneVO

    // 将选中的产品ID数组转换为JSON字符串
    data.productIds = JSON.stringify(selectedProductIds.value || [])

    if (formType.value === 'create') {
      await LogisticsZoneApi.createLogisticsZone(data)
      message.success(t('common.createSuccess'))
    } else {
      await LogisticsZoneApi.updateLogisticsZone(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    countryCode: undefined,
    productIds: undefined,
    zoneCode: undefined,
    zoneName: undefined,
    postalCodes: undefined,
    restrictionType: undefined, //限制类型 (FORBIDDEN-禁止配送, REMOTE_FEE-偏远地区费, NORMAL-正常)
    stateProvince: undefined, //一级行政区划 (州/省/自治区等)
    city: undefined, //二级行政区划 (城市/地区等)
    district: undefined, //三级行政区划 (区/县等)
    specialAreaType: undefined, //特殊区域类型 (ISLAND-岛屿, TERRITORY-领土, MILITARY-军事基地等)
    fullAreaName: undefined, //完整区域描述 (用于显示和搜索，如：California Los Angeles)
    feeFormula: undefined, //附加费公式 (如：3*weight_kg,min:48 表示每公斤3元，最低48元)
    remark: undefined,
    sort: undefined,
    status: 0
  }
  // 重置选中的产品ID数组
  selectedProductIds.value = []
  formRef.value?.resetFields()
}

// 监听选中的产品ID变化，同步到表单数据
watch(
  () => selectedProductIds.value,
  (newValue) => {
    if (newValue && newValue.length > 0) {
      formData.value.productIds = JSON.stringify(newValue)
    } else {
      formData.value.productIds = undefined
    }
  },
  { deep: true }
)
</script>
