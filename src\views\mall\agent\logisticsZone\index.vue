<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="国家编码" prop="countryCode">
        <el-input
          v-model="queryParams.countryCode"
          placeholder="请输入国家编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="产品编号" prop="productId">
        <el-input
          v-model="queryParams.productId"
          placeholder="请输入产品编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="分区编码" prop="zoneCode">
        <el-input
          v-model="queryParams.zoneCode"
          placeholder="请输入分区编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="分区名称" prop="zoneName">
        <el-input
          v-model="queryParams.zoneName"
          placeholder="请输入分区名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="开启状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择开启状态"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['agent:logistics-zone:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['agent:logistics-zone:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="分类编号" align="center" prop="id" />
      <el-table-column label="国家编码" align="center" prop="countryCode" />
      <el-table-column label="产品编号" align="center" prop="productIds" min-width="120px">
        <template #default="scope">
          <div v-if="scope.row.productIds">
            <el-tag
              v-for="productId in parseProductIds(scope.row.productIds)"
              :key="productId"
              size="small"
              class="mr-1 mb-1"
            >
              {{ getProductName(productId) }}
            </el-tag>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="分区编码" align="center" prop="zoneCode" />
      <el-table-column label="分区名称" align="center" prop="zoneName" />
      <el-table-column label="开启状态" align="center" prop="status" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['agent:logistics-zone:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['agent:logistics-zone:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <LogisticsZoneForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { LogisticsZoneApi, LogisticsZoneVO } from '@/api/mall/agent/logisticsZone'
import { LogisticsProductApi } from '@/api/mall/agent/logisticsProduct'
import LogisticsZoneForm from './LogisticsZoneForm.vue'

/** 代购物流国家分区 列表 */
defineOptions({ name: 'LogisticsZone' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<LogisticsZoneVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  countryCode: undefined,
  productId: undefined,
  zoneCode: undefined,
  zoneName: undefined,
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 产品列表
const productList = ref<any[]>([])

/** 解析产品ID字符串 */
const parseProductIds = (productIdsStr: string): number[] => {
  try {
    return JSON.parse(productIdsStr)
  } catch (e) {
    console.warn('解析产品ID失败:', e)
    return []
  }
}

/** 获取产品名称 */
const getProductName = (productId: number): string => {
  const product = productList.value.find((p) => p.id === productId)
  return product ? `${product.id} (${product.memo})` : `ID:${productId}`
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await LogisticsZoneApi.getLogisticsZonePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await LogisticsZoneApi.deleteLogisticsZone(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await LogisticsZoneApi.exportLogisticsZone(queryParams)
    download.excel(data, '代购物流国家分区.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  // 加载产品列表
  try {
    productList.value = await LogisticsProductApi.getSimpleLogisticsProductList()
  } catch (e) {
    console.warn('加载产品列表失败:', e)
  }
  // 加载分区列表
  getList()
})
</script>
