<template>
  <Dialog v-model="dialogVisible" title="订单项入库" width="1200px">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <!-- 商品信息展示 -->
      <el-form-item label="商品信息">
        <div class="flex items-center">
          <el-image
            v-if="orderItem?.picUrl"
            :src="orderItem.picUrl"
            class="!h-[60px] !w-[60px] mr-3"
            fit="contain"
          />
          <div>
            <div class="font-medium text-base">{{ orderItem?.spuName }}</div>
            <div class="text-sm text-gray-500 mt-1">
              <span class="mr-4">订单数量：{{ orderItem?.count }}</span>
              <span>实付金额：¥{{ (orderItem?.payPrice || 0) / 100 }}</span>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="仓库" prop="warehouseId">
            <el-select v-model="formData.warehouseId" placeholder="请选择仓库" style="width: 100%">
              <el-option
                v-for="item in props.warehouseList"
                :key="item.id"
                :value="item.id"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="库位" prop="location">
            <el-input v-model="formData.location" placeholder="请输入库位" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="入库数量" prop="count">
            <el-input-number
              v-model="formData.count"
              :min="1"
              :max="orderItem?.count || 999"
              placeholder="请输入入库数量"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="商品分类"
            prop="categoryId"
            :rules="[{ required: true, message: '请选择商品分类', trigger: 'change' }]"
          >
            <div class="flex items-center">
              <el-input
                :model-value="getCategoryDisplayName(formData.categoryId)"
                placeholder="请选择商品分类"
                readonly
                style="width: 100%"
                @click="openCategorySelector()"
              />
              <el-button type="primary" size="small" class="ml-2" @click="openCategorySelector()">
                选择
              </el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="重量(g)" prop="weight">
            <el-input-number
              v-model="formData.weight"
              :min="0"
              placeholder="请输入重量"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="长度(cm)" prop="length">
            <el-input-number
              v-model="formData.length"
              :min="0"
              :precision="2"
              placeholder="请输入长度"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="宽度(cm)" prop="width">
            <el-input-number
              v-model="formData.width"
              :min="0"
              :precision="2"
              placeholder="请输入宽度"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="高度(cm)" prop="height">
            <el-input-number
              v-model="formData.height"
              :min="0"
              :precision="2"
              placeholder="请输入高度"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="预计包装重量g" prop="height">
            <el-input-number
              v-model="formData.prePackageWeight"
              :min="0"
              placeholder="请输入预计包装重量"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="预计包装费">
            <el-select
              v-model="formData.prePackagePrice"
              filterable
              allow-create
              default-first-option
              placeholder="请选择或输入"
              :reserve-keyword="false"
              @create="handleCreate"
            >
              <el-option label="0.00" value="0" />
              <el-option label="5.00" value="500" />
              <el-option label="10.00" value="1000" />
              <el-option label="15.00" value="1500" />
              <el-option label="20.00" value="2000" />
              <el-option label="25.00" value="2500" />
              <el-option label="30.00" value="3000" />
              <el-option label="35.00" value="3500" />
              <el-option label="40.00" value="4000" />
              <el-option label="45.00" value="4500" />
              <el-option label="50.00" value="5000" />
              <el-option label="60.00" value="6000" />
              <el-option label="70.00" value="7000" />
              <el-option label="80.00" value="8000" />
              <el-option label="90.00" value="9000" />
              <el-option label="100.00" value="10000" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否能合并发货" prop="canCombine">
            <el-switch v-model="formData.canCombine" style="user-select: none" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="免费服务">
            <el-tag v-for="serve in orderItemDetail.freeServerList" :key="serve.id">{{
              serve.name
            }}</el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收费服务">
            <el-tag v-for="serve in orderItemDetail.chargeServerList" :key="serve.id">{{
              serve.name
            }}</el-tag>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="质检图片" prop="inspectPicUrls">
        <UploadImgs v-model="formData.inspectPicUrls" :limit="10" />
        <div class="text-xs text-gray-500 mt-1">最多上传10张图片</div>
      </el-form-item>

      <el-form-item label="质检视频" prop="inspectVideoUrls">
        <UploadImgs
          v-model="formData.inspectVideoUrls"
          :limit="6"
          :file-type="['video/mp4', 'video/avi', 'video/mov', 'video/wmv']"
        />
        <div class="text-xs text-gray-500 mt-1">最多上传6个视频</div>
      </el-form-item>

      <el-form-item label="相关文件" prop="fileUrl">
        <UploadFile v-model="formData.fileUrl" />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <!-- 商品分类选择弹窗 -->
  <CategorySelector ref="categorySelectorRef" @change="handleCategoryChange" />
</template>

<script lang="ts" setup>
import * as TradeOrderApi from '@/api/mall/trade/order'
import { UploadImgs, UploadFile } from '@/components/UploadFile'
import CategorySelector from '@/components/CategorySelector/index.vue'
import { CategoryApi, type CategoryVO } from '@/api/mall/agent/category'

defineOptions({ name: 'OrderItemInboundForm' })

// 定义props
interface Props {
  warehouseList?: Array<{
    id: number
    name: string
  }>
}

const props = withDefaults(defineProps<Props>(), {
  warehouseList: () => []
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const orderItem = ref<TradeOrderApi.OrderItemRespVO>() // 当前订单项信息

const orderDetail = ref({ items: [] }) // 订单详情
const orderItemDetail = ref({}) // 订单项详情

const formData = ref<TradeOrderApi.InboundItemVO>({
  itemId: 0,
  warehouseId: 0,
  location: '',
  count: 1,
  categoryId: undefined, // 商品分类ID
  weight: 0,
  length: 0,
  width: 0,
  height: 0,
  inspectPicUrls: [],
  inspectVideoUrls: [],
  fileUrl: '',
  remark: '',
  canCombine: true,
  prePackageWeight: 0,
  prePackagePrice: 0
})

const formRules = reactive({
  warehouseId: [{ required: true, message: '请选择仓库', trigger: 'change' }],
  location: [{ required: true, message: '请输入库位', trigger: 'blur' }],
  count: [{ required: true, message: '请输入入库数量', trigger: 'blur' }],
  categoryId: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
  weight: [{ required: true, message: '请输入重量', trigger: 'blur' }],
  length: [{ required: true, message: '请输入长度', trigger: 'blur' }],
  width: [{ required: true, message: '请输入宽度', trigger: 'blur' }],
  height: [{ required: true, message: '请输入高度', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

const categorySelectorRef = ref() // 分类选择器 Ref

// 分类相关数据
const categoryList = ref<CategoryVO[]>([]) // 分类列表

/** 打开弹窗 */
const open = async (row: TradeOrderApi.OrderItemRespVO) => {
  resetForm()
  orderItem.value = row
  formData.value.itemId = row.id || 0
  formData.value.count = row.count || 1
  dialogVisible.value = true

  // 默认选中第一个仓库
  if (props.warehouseList.length > 0) {
    formData.value.warehouseId = props.warehouseList[0].id
  }

  // 获取订单详情 获取免费服务增值服务
  const res = await TradeOrderApi.getOrder(row.orderId)
  // 没有表单信息则关闭页面返回
  if (!res) {
    message.error('交易订单不存在')
    close()
  }
  orderDetail.value = res
  console.log(orderDetail.value)
  orderDetail.value.items.forEach((item) => {
    if (item.id === row.id) {
      orderItemDetail.value = item
    }
  })
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  // 提交请求
  formLoading.value = true
  try {
    const batchData: TradeOrderApi.BatchInboundVO = {
      items: [formData.value]
    }

    await TradeOrderApi.inboundOrderItem(batchData)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success', true)
  } finally {
    formLoading.value = false
  }
}

/** 分类相关方法 */
// 打开分类选择器
const openCategorySelector = () => {
  const currentCategoryId = formData.value.categoryId
  categorySelectorRef.value?.open(currentCategoryId)
}

// 处理分类选择变化
const handleCategoryChange = (category: CategoryVO | undefined) => {
  formData.value.categoryId = category?.id
}

// 获取分类显示名称（父类/子类格式）
const getCategoryDisplayName = (categoryId: number | undefined): string => {
  if (!categoryId) return ''

  // 优先使用CategorySelector组件的方法
  if (categorySelectorRef.value?.getCategoryFullDisplayName) {
    return categorySelectorRef.value.getCategoryFullDisplayName(categoryId)
  }

  // 备用方案：在本地分类列表中查找
  const category = categoryList.value.find((c) => c.id === categoryId)
  if (category) {
    // 如果有父分类ID，查找父分类名称
    if (category.parentId && category.parentId > 0) {
      const parentCategory = categoryList.value.find((p) => p.id === category.parentId)
      if (parentCategory) {
        return `${parentCategory.nameZh}/${category.nameZh}`
      }
    }
    return category.nameZh
  }

  return `ID:${categoryId}`
}

// 加载分类列表
const loadCategoryList = async () => {
  try {
    const response = await CategoryApi.getCategoryList({})
    categoryList.value = response
  } catch (error) {
    console.error('加载分类列表失败:', error)
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    itemId: 0,
    warehouseId: 0,
    location: '',
    count: 1,
    categoryId: undefined, // 商品分类ID
    weight: 0,
    length: 0,
    width: 0,
    height: 0,
    inspectPicUrls: [],
    inspectVideoUrls: [],
    fileUrl: '',
    remark: '',
    canCombine: true,
    prePackageWeight: 0,
    prePackagePrice: 0
  }
  orderItem.value = undefined
  formRef.value?.resetFields()
}

// 组件挂载时加载分类列表
onMounted(() => {
  loadCategoryList()
})

// 方法定义（在 setup 或 methods 中）
const handleCreate = (value) => {
  // 正则：匹配正整数（不能以 0 开头，除非就是 "0"）
  const positiveIntegerPattern = /^(0|[1-9]\d*)$/

  if (!positiveIntegerPattern.test(value)) {
    ElMessage.error('仅允许输入正整数（如：100、500、1000）')
    // 阻止非法值创建
    throw new Error('Invalid input')
  }

  // 可选：限制最大值，比如不超过 99999
  const numValue = Number(value)
  if (numValue > 99999) {
    ElMessage.error('输入值不能超过 99999')
    throw new Error('Value too large')
  }

  // 合法值，允许创建（el-select 会自动使用这个 value）
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 18px;
}
</style>
